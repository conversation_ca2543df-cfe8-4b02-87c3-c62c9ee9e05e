// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id        String   @id @default(cuid())
  clerkId   String   @unique
  email     String   @unique
  firstName String?
  lastName  String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ownedRestaurants   Restaurant[]        @relation("RestaurantOwner")
  favorites          Favorite[]
  halalVerifications HalalVerification[]

  @@index([clerkId])
  @@index([email])
}

model Restaurant {
  id          String   @id @default(cuid())
  name        String
  address     String
  city        String
  state       String
  zipcode     String
  slug        String   @unique
  type        String?  @default("Dine-in")
  phone       String?
  website     String?
  cuisines    String[]
  highlights  String[]
  lat         Float?
  lng         Float?
  isPublished Boolean  @default(false)
  isFeatured  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  ownerId            String?
  owner              User?               @relation("RestaurantOwner", fields: [ownerId], references: [id])
  favorites          Favorite[]
  halalVerifications HalalVerification[]

  @@index([slug])
  @@index([ownerId])
}

model Favorite {
  id           String   @id @default(cuid())
  userId       String
  restaurantId String
  createdAt    DateTime @default(now())

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  restaurant Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)

  @@unique([userId, restaurantId])
  @@index([userId])
  @@index([restaurantId])
}

model HalalVerification {
  id           String   @id @default(cuid())
  userId       String
  restaurantId String
  isHalal      Boolean // true = user thinks it's halal, false = user thinks it's not halal
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  restaurant Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)

  @@unique([userId, restaurantId])
  @@index([userId])
  @@index([restaurantId])
  @@index([isHalal])
}

enum UserRole {
  ADMIN
  OWNER
  USER
}
