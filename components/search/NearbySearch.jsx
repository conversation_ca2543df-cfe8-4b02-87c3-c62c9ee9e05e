"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Navigation } from "lucide-react";
import { toast } from "sonner";
import { getCurrentLocation, isGeolocationSupported } from "@/lib/location";

export function NearbySearch({
  onLocationSearch,
  radius = 10,
  className = "",
}) {
  const [isLoading, setIsLoading] = useState(false);

  const handleNearbySearch = async () => {
    if (!isGeolocationSupported()) {
      toast.error("Geolocation is not supported by your browser");
      return;
    }

    setIsLoading(true);

    try {
      const location = await getCurrentLocation();

      // Call the parent component's search function
      await onLocationSearch({
        latitude: location.latitude,
        longitude: location.longitude,
        radius: radius,
      });

      toast.success(
        `Found restaurants within ${radius} miles of your location`
      );
    } catch (error) {
      console.error("Location error:", error);

      if (error.message.includes("denied")) {
        toast.error(
          "Location access denied. Please enable location permissions and try again."
        );
      } else if (error.message.includes("unavailable")) {
        toast.error(
          "Your location is currently unavailable. Please try again later."
        );
      } else if (error.message.includes("timeout")) {
        toast.error("Location request timed out. Please try again.");
      } else {
        toast.error(
          "Unable to get your location. Please try again or search manually."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Check geolocation support on client side only
  if (typeof window !== "undefined" && !isGeolocationSupported()) {
    return null; // Don't show the button if geolocation isn't supported
  }

  return (
    <Button
      type="button"
      variant="outline"
      onClick={handleNearbySearch}
      disabled={isLoading}
      className={`flex items-center gap-2 font-medium ${className}`}
      title={`Find restaurants within ${radius} miles of your location`}
    >
      {isLoading ? (
        <Loader2 className="w-4 h-4 animate-spin" />
      ) : (
        <Navigation className="w-4 h-4" />
      )}
      <span className="hidden sm:inline">Near Me</span>
    </Button>
  );
}

// Compact version for mobile or smaller spaces
export function NearbySearchButton({ onLocationSearch, className = "" }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleQuickNearbySearch = async () => {
    if (!isGeolocationSupported()) {
      toast.error("Geolocation is not supported by your browser");
      return;
    }

    setIsLoading(true);

    try {
      const location = await getCurrentLocation();

      // Use default 10-mile radius for quick search
      await onLocationSearch({
        latitude: location.latitude,
        longitude: location.longitude,
        radius: 10,
      });

      toast.success("Found restaurants within 10 miles of your location");
    } catch (error) {
      console.error("Location error:", error);
      toast.error(
        "Unable to get your location. Please try the detailed search."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Check geolocation support on client side only
  if (typeof window !== "undefined" && !isGeolocationSupported()) {
    return null;
  }

  return (
    <Button
      type="button"
      variant="ghost"
      size="sm"
      onClick={handleQuickNearbySearch}
      disabled={isLoading}
      className={`flex items-center gap-1 font-medium hover:bg-blue-50 hover:text-blue-600 transition-colors ${className}`}
      title={`Find restaurants near me (${radius} mile radius)`}
    >
      {isLoading ? (
        <Loader2 className="w-4 h-4 animate-spin" />
      ) : (
        <Navigation className="w-4 h-4" />
      )}
      <span className="hidden sm:inline">Near Me</span>
    </Button>
  );
}
