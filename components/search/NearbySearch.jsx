"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MapPin, Loader2, Navigation } from "lucide-react";
import { toast } from "sonner";
import { getCurrentLocation, RADIUS_OPTIONS, isGeolocationSupported } from "@/lib/location";

export function NearbySearch({ onLocationSearch, className = "" }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRadius, setSelectedRadius] = useState("10");

  const handleNearbySearch = async () => {
    if (!isGeolocationSupported()) {
      toast.error("Geolocation is not supported by your browser");
      return;
    }

    setIsLoading(true);

    try {
      const location = await getCurrentLocation();
      
      // Call the parent component's search function
      await onLocationSearch({
        latitude: location.latitude,
        longitude: location.longitude,
        radius: parseInt(selectedRadius)
      });

      toast.success(`Found restaurants within ${selectedRadius} miles of your location`);
      setIsOpen(false);
    } catch (error) {
      console.error("Location error:", error);
      
      if (error.message.includes("denied")) {
        toast.error("Location access denied. Please enable location permissions and try again.");
      } else if (error.message.includes("unavailable")) {
        toast.error("Your location is currently unavailable. Please try again later.");
      } else if (error.message.includes("timeout")) {
        toast.error("Location request timed out. Please try again.");
      } else {
        toast.error("Unable to get your location. Please try again or search manually.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isGeolocationSupported()) {
    return null; // Don't show the button if geolocation isn't supported
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className={`flex items-center gap-2 ${className}`}>
          <Navigation className="w-4 h-4" />
          Near Me
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-primary" />
            Find Nearby Restaurants
          </DialogTitle>
          <DialogDescription>
            We'll use your current location to find halal restaurants near you.
            Your location data is not stored or shared.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="radius" className="text-sm font-medium">
              Search Radius
            </label>
            <Select value={selectedRadius} onValueChange={setSelectedRadius}>
              <SelectTrigger>
                <SelectValue placeholder="Select radius" />
              </SelectTrigger>
              <SelectContent>
                {RADIUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2">
            <Button 
              onClick={handleNearbySearch} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Getting Location...
                </>
              ) : (
                <>
                  <Navigation className="w-4 h-4 mr-2" />
                  Find Nearby Restaurants
                </>
              )}
            </Button>
            
            <p className="text-xs text-muted-foreground text-center">
              This will request access to your location
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Compact version for mobile or smaller spaces
export function NearbySearchButton({ onLocationSearch, className = "" }) {
  const [isLoading, setIsLoading] = useState(false);

  const handleQuickNearbySearch = async () => {
    if (!isGeolocationSupported()) {
      toast.error("Geolocation is not supported by your browser");
      return;
    }

    setIsLoading(true);

    try {
      const location = await getCurrentLocation();
      
      // Use default 10-mile radius for quick search
      await onLocationSearch({
        latitude: location.latitude,
        longitude: location.longitude,
        radius: 10
      });

      toast.success("Found restaurants within 10 miles of your location");
    } catch (error) {
      console.error("Location error:", error);
      toast.error("Unable to get your location. Please try the detailed search.");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isGeolocationSupported()) {
    return null;
  }

  return (
    <Button 
      variant="ghost" 
      size="sm"
      onClick={handleQuickNearbySearch}
      disabled={isLoading}
      className={`flex items-center gap-1 ${className}`}
      title="Find restaurants near me (10 mile radius)"
    >
      {isLoading ? (
        <Loader2 className="w-4 h-4 animate-spin" />
      ) : (
        <Navigation className="w-4 h-4" />
      )}
      <span className="hidden sm:inline">Near Me</span>
    </Button>
  );
}
