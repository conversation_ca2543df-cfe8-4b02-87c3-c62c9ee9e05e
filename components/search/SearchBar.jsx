"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, MapPin, Utensils } from "lucide-react";
import { NearbySearch } from "@/components/search/NearbySearch";

const popularCuisines = [
  "Indian",
  "Pakistani",
  "Italian",
  "Mexican",
  "Chinese",
  "American",
  "Thai",
  "Mediterranean",
  "French",
];

const popularLocations = [
  "New York",
  "Los Angeles",
  "Chicago",
  "Houston",
  "Detroit",
  "Philadelphia",
  "San Antonio",
  "San Diego",
  "Washington",
  "San Francisco",
  "Seattle",
  "Dallas",
  "Boston",
  "Orlando",
  "Austin",
  "Atlanta",
];

export function SearchBar({ className = "", size = "default" }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [locationQuery, setLocationQuery] = useState("");
  const router = useRouter();

  const handleSearch = (e) => {
    e.preventDefault();

    const params = new URLSearchParams();
    if (searchQuery.trim()) params.set("q", searchQuery.trim());
    if (locationQuery.trim()) params.set("location", locationQuery.trim());

    router.push(`/search?${params.toString()}`);
  };

  const handleQuickSearch = (type, value) => {
    if (type === "cuisine") {
      setSearchQuery(value);
    } else if (type === "location") {
      setLocationQuery(value);
    }
  };

  const handleLocationSearch = async (locationData) => {
    // Navigate to a special nearby search results page
    const params = new URLSearchParams();
    params.set("lat", locationData.latitude.toString());
    params.set("lng", locationData.longitude.toString());
    params.set("radius", locationData.radius.toString());
    params.set("nearby", "true");

    router.push(`/search?${params.toString()}`);
  };

  const isLarge = size === "large";

  return (
    <div className={`w-full ${className}`}>
      <Card className={`p-6 ${isLarge ? "shadow-lg" : "shadow-md"}`}>
        <form onSubmit={handleSearch} className="space-y-4">
          <div
            className={`grid gap-4 ${
              isLarge ? "md:grid-cols-3" : "md:grid-cols-2"
            }`}
          >
            {/* Search Input */}
            <div className="relative">
              <Utensils className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                type="text"
                placeholder="Search restaurants, cuisines..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`pl-10 ${isLarge ? "h-12 text-lg" : "h-10"}`}
              />
            </div>

            {/* Location Input */}
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                type="text"
                placeholder="City, State or ZIP"
                value={locationQuery}
                onChange={(e) => setLocationQuery(e.target.value)}
                className={`pl-10 ${isLarge ? "h-12 text-lg" : "h-10"}`}
              />
            </div>

            {/* Search Button */}
            <div className="flex gap-2">
              <Button
                type="submit"
                className={`${
                  isLarge ? "h-12 text-lg px-8" : "h-10"
                } flex items-center gap-2 flex-1`}
              >
                <Search className="w-4 h-4" />
                Search
              </Button>

              {/* Nearby Search Button */}
              <NearbySearch
                onLocationSearch={handleLocationSearch}
                className={isLarge ? "h-12" : "h-10"}
              />
            </div>
          </div>
        </form>

        {/* Quick Search Options */}
        {isLarge && (
          <div className="mt-6 space-y-4">
            {/* Popular Cuisines */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">
                Popular Cuisines
              </h4>
              <div className="flex flex-wrap gap-2">
                {popularCuisines.slice(0, 8).map((cuisine) => (
                  <Badge
                    key={cuisine}
                    variant="outline"
                    className="cursor-pointer hover:bg-secondary transition-colors"
                    onClick={() => handleQuickSearch("cuisine", cuisine)}
                  >
                    {cuisine}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Popular Locations */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">
                Popular Locations
              </h4>
              <div className="flex flex-wrap gap-2">
                {popularLocations.slice(0, 8).map((location) => (
                  <Badge
                    key={location}
                    variant="outline"
                    className="cursor-pointer hover:bg-secondary transition-colors"
                    onClick={() => handleQuickSearch("location", location)}
                  >
                    {location}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
