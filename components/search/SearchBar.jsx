"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Badge } from "@/components/ui/badge";
import { Search, MapPin, Utensils } from "lucide-react";
import { NearbySearch } from "@/components/search/NearbySearch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RADIUS_OPTIONS } from "@/lib/location";

const popularCuisines = [
  "Indian",
  "Pakistani",
  "Italian",
  "Mexican",
  "Chinese",
  "American",
  "Thai",
  "Mediterranean",
  "French",
];

const popularLocations = [
  "New York",
  "Los Angeles",
  "Chicago",
  "Houston",
  "Detroit",
  "Philadelphia",
  "San Antonio",
  "San Diego",
  "Washington",
  "San Francisco",
  "Seattle",
  "Dallas",
  "Boston",
  "Orlando",
  "Austin",
  "Atlanta",
];

export function SearchBar({ className = "", size = "default" }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [locationQuery, setLocationQuery] = useState("");
  const [nearbyRadius, setNearbyRadius] = useState("50");
  const router = useRouter();

  const handleSearch = (e) => {
    e.preventDefault();

    const params = new URLSearchParams();
    if (searchQuery.trim()) params.set("q", searchQuery.trim());
    if (locationQuery.trim()) {
      params.set("location", locationQuery.trim());
      params.set("radius", nearbyRadius);
      params.set("useRadius", "true");
    }

    router.push(`/search?${params.toString()}`);
  };

  const handleQuickSearch = (type, value) => {
    if (type === "cuisine") {
      setSearchQuery(value);
    } else if (type === "location") {
      setLocationQuery(value);
    }
  };

  const handleLocationSearch = async (locationData) => {
    // Navigate to a special nearby search results page
    const params = new URLSearchParams();
    params.set("lat", locationData.latitude.toString());
    params.set("lng", locationData.longitude.toString());
    params.set("radius", locationData.radius.toString());
    params.set("nearby", "true");

    router.push(`/search?${params.toString()}`);
  };

  const isLarge = size === "large";

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`bg-white rounded-2xl border border-gray-200 ${
          isLarge ? "shadow-xl p-8" : "shadow-lg p-6"
        } backdrop-blur-sm`}
      >
        <form onSubmit={handleSearch} className="space-y-6">
          {/* Main Search Row */}
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Primary Search Input */}
            <div className="flex-1 relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Utensils className="h-5 w-5 text-gray-400 group-focus-within:text-primary transition-colors" />
              </div>
              <Input
                type="text"
                placeholder="What are you craving? (e.g., biryani, pizza, halal burgers...)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`pl-12 pr-4 border-gray-300 focus:border-primary focus:ring-primary rounded-xl transition-all duration-200 ${
                  isLarge ? "h-14 text-lg" : "h-12"
                } bg-gray-50 focus:bg-white hover:bg-white`}
              />
            </div>

            {/* Location Input */}
            <div className="lg:w-80 relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <MapPin className="h-5 w-5 text-gray-400 group-focus-within:text-primary transition-colors" />
              </div>
              <Input
                type="text"
                placeholder="Location (City, ZIP)"
                value={locationQuery}
                onChange={(e) => setLocationQuery(e.target.value)}
                className={`pl-12 pr-4 border-gray-300 focus:border-primary focus:ring-primary rounded-xl transition-all duration-200 ${
                  isLarge ? "h-14 text-lg" : "h-12"
                } bg-gray-50 focus:bg-white hover:bg-white`}
              />
            </div>
          </div>

          {/* Action Row */}
          <div className="flex flex-col sm:flex-row items-center gap-4">
            {/* Radius Selector */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
                Search radius:
              </span>
              <Select value={nearbyRadius} onValueChange={setNearbyRadius}>
                <SelectTrigger
                  className={`w-32 border-gray-300 rounded-lg ${
                    isLarge ? "h-10" : "h-9"
                  } bg-gray-50 hover:bg-white transition-colors`}
                >
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {RADIUS_OPTIONS.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value.toString()}
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 ml-auto">
              <NearbySearch
                onLocationSearch={handleLocationSearch}
                radius={parseInt(nearbyRadius)}
                className={`${
                  isLarge ? "h-12 px-6" : "h-10 px-4"
                } bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-200 shadow-md hover:shadow-lg`}
              />

              <Button
                type="submit"
                className={`${
                  isLarge ? "h-12 px-8 text-lg" : "h-10 px-6"
                } bg-primary hover:bg-primary/90 text-white rounded-xl transition-all duration-200 shadow-md hover:shadow-lg flex items-center gap-2 font-medium`}
              >
                <Search className="w-5 h-5" />
                Search
              </Button>
            </div>
          </div>
        </form>

        {/* Quick Search Options */}
        {isLarge && (
          <div className="mt-8 pt-6 border-t border-gray-100">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Popular Cuisines */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <Utensils className="w-4 h-4" />
                  Popular Cuisines
                </h4>
                <div className="flex flex-wrap gap-2">
                  {popularCuisines.slice(0, 8).map((cuisine) => (
                    <Badge
                      key={cuisine}
                      variant="outline"
                      className="cursor-pointer hover:bg-primary hover:text-white hover:border-primary transition-all duration-200 px-3 py-1 rounded-full text-sm font-medium border-gray-300"
                      onClick={() => handleQuickSearch("cuisine", cuisine)}
                    >
                      {cuisine}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Popular Locations */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  Popular Locations
                </h4>
                <div className="flex flex-wrap gap-2">
                  {popularLocations.slice(0, 8).map((location) => (
                    <Badge
                      key={location}
                      variant="outline"
                      className="cursor-pointer hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200 px-3 py-1 rounded-full text-sm font-medium border-gray-300"
                      onClick={() => handleQuickSearch("location", location)}
                    >
                      {location}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
