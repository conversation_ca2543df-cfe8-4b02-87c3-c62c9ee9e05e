"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Share2, 
  Facebook, 
  Twitter, 
  MessageCircle, 
  Mail, 
  Copy, 
  Check 
} from "lucide-react";
import { toast } from "sonner";

export function SocialShare({ 
  url, 
  title, 
  description, 
  hashtags = [],
  className = "" 
}) {
  const [copied, setCopied] = useState(false);

  // Encode URL and text for sharing
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const hashtagString = hashtags.length > 0 ? hashtags.map(tag => `#${tag}`).join(' ') : '';
  const encodedHashtags = encodeURIComponent(hashtagString);

  // Social platform URLs
  const shareUrls = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedTitle}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&hashtags=${hashtags.join(',')}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
    email: `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A%0A${url}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
    reddit: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`
  };

  // Copy to clipboard function
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  // Open share URL in new window
  const openShareWindow = (shareUrl) => {
    window.open(
      shareUrl,
      'share-dialog',
      'width=600,height=400,resizable=yes,scrollbars=yes'
    );
  };

  // Native Web Share API (for mobile)
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description,
          url: url,
        });
      } catch (err) {
        if (err.name !== 'AbortError') {
          toast.error("Sharing failed");
        }
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <Share2 className="w-4 h-4 mr-2" />
          Share
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {/* Native share (mobile) */}
        {navigator.share && (
          <DropdownMenuItem onClick={handleNativeShare}>
            <Share2 className="w-4 h-4 mr-2" />
            Share...
          </DropdownMenuItem>
        )}
        
        {/* Facebook */}
        <DropdownMenuItem onClick={() => openShareWindow(shareUrls.facebook)}>
          <Facebook className="w-4 h-4 mr-2 text-blue-600" />
          Facebook
        </DropdownMenuItem>

        {/* Twitter */}
        <DropdownMenuItem onClick={() => openShareWindow(shareUrls.twitter)}>
          <Twitter className="w-4 h-4 mr-2 text-blue-400" />
          Twitter
        </DropdownMenuItem>

        {/* WhatsApp */}
        <DropdownMenuItem onClick={() => openShareWindow(shareUrls.whatsapp)}>
          <MessageCircle className="w-4 h-4 mr-2 text-green-500" />
          WhatsApp
        </DropdownMenuItem>

        {/* LinkedIn */}
        <DropdownMenuItem onClick={() => openShareWindow(shareUrls.linkedin)}>
          <div className="w-4 h-4 mr-2 bg-blue-700 rounded-sm flex items-center justify-center">
            <span className="text-white text-xs font-bold">in</span>
          </div>
          LinkedIn
        </DropdownMenuItem>

        {/* Telegram */}
        <DropdownMenuItem onClick={() => openShareWindow(shareUrls.telegram)}>
          <div className="w-4 h-4 mr-2 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs">T</span>
          </div>
          Telegram
        </DropdownMenuItem>

        {/* Reddit */}
        <DropdownMenuItem onClick={() => openShareWindow(shareUrls.reddit)}>
          <div className="w-4 h-4 mr-2 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">R</span>
          </div>
          Reddit
        </DropdownMenuItem>

        {/* Email */}
        <DropdownMenuItem onClick={() => window.location.href = shareUrls.email}>
          <Mail className="w-4 h-4 mr-2 text-gray-600" />
          Email
        </DropdownMenuItem>

        {/* Copy Link */}
        <DropdownMenuItem onClick={copyToClipboard}>
          {copied ? (
            <Check className="w-4 h-4 mr-2 text-green-500" />
          ) : (
            <Copy className="w-4 h-4 mr-2" />
          )}
          {copied ? "Copied!" : "Copy Link"}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact version for restaurant cards
export function SocialShareButton({ 
  url, 
  title, 
  description, 
  hashtags = [],
  className = "" 
}) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success("Link copied!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description,
          url: url,
        });
      } catch (err) {
        if (err.name !== 'AbortError') {
          // Fallback to copy
          copyToClipboard();
        }
      }
    } else {
      // Fallback to copy
      copyToClipboard();
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleNativeShare}
      className={`p-2 hover:bg-blue-50 ${className}`}
      title="Share restaurant"
    >
      {copied ? (
        <Check className="w-4 h-4 text-green-500" />
      ) : (
        <Share2 className="w-4 h-4 text-gray-400 hover:text-blue-500" />
      )}
    </Button>
  );
}
