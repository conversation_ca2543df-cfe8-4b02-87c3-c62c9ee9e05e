"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Share2, Co<PERSON>, Check } from "lucide-react";
import { toast } from "sonner";
import {
  FacebookShareButton,
  TwitterShareButton,
  WhatsappShareButton,
  LinkedinShareButton,
  TelegramShareButton,
  RedditShareButton,
  EmailShareButton,
  FacebookIcon,
  TwitterIcon,
  WhatsappIcon,
  LinkedinIcon,
  TelegramIcon,
  RedditIcon,
  EmailIcon,
} from "react-share";

export function SocialShare({
  url,
  title,
  description,
  hashtags = [],
  className = "",
}) {
  const [copied, setCopied] = useState(false);

  // Copy to clipboard function
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  // Native Web Share API (for mobile)
  const handleNativeShare = async () => {
    if (typeof navigator !== "undefined" && navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description,
          url: url,
        });
      } catch (err) {
        if (err.name !== "AbortError") {
          toast.error("Sharing failed");
        }
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <Share2 className="w-4 h-4 mr-2" />
          Share
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {/* Native share (mobile) */}
        {typeof navigator !== "undefined" && navigator.share && (
          <DropdownMenuItem onClick={handleNativeShare}>
            <Share2 className="w-4 h-4 mr-2" />
            Share...
          </DropdownMenuItem>
        )}

        {/* Facebook */}
        <DropdownMenuItem asChild>
          <FacebookShareButton url={url} quote={title} className="w-full">
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <FacebookIcon size={16} round className="mr-2" />
              Facebook
            </div>
          </FacebookShareButton>
        </DropdownMenuItem>

        {/* Twitter */}
        <DropdownMenuItem asChild>
          <TwitterShareButton
            url={url}
            title={title}
            hashtags={hashtags}
            className="w-full"
          >
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <TwitterIcon size={16} round className="mr-2" />
              Twitter
            </div>
          </TwitterShareButton>
        </DropdownMenuItem>

        {/* WhatsApp */}
        <DropdownMenuItem asChild>
          <WhatsappShareButton url={url} title={title} className="w-full">
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <WhatsappIcon size={16} round className="mr-2" />
              WhatsApp
            </div>
          </WhatsappShareButton>
        </DropdownMenuItem>

        {/* LinkedIn */}
        <DropdownMenuItem asChild>
          <LinkedinShareButton
            url={url}
            title={title}
            summary={description}
            className="w-full"
          >
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <LinkedinIcon size={16} round className="mr-2" />
              LinkedIn
            </div>
          </LinkedinShareButton>
        </DropdownMenuItem>

        {/* Telegram */}
        <DropdownMenuItem asChild>
          <TelegramShareButton url={url} title={title} className="w-full">
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <TelegramIcon size={16} round className="mr-2" />
              Telegram
            </div>
          </TelegramShareButton>
        </DropdownMenuItem>

        {/* Reddit */}
        <DropdownMenuItem asChild>
          <RedditShareButton url={url} title={title} className="w-full">
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <RedditIcon size={16} round className="mr-2" />
              Reddit
            </div>
          </RedditShareButton>
        </DropdownMenuItem>

        {/* Email */}
        <DropdownMenuItem asChild>
          <EmailShareButton
            url={url}
            subject={title}
            body={description}
            className="w-full"
          >
            <div className="flex items-center w-full px-2 py-1.5 text-sm">
              <EmailIcon size={16} round className="mr-2" />
              Email
            </div>
          </EmailShareButton>
        </DropdownMenuItem>

        {/* Copy Link */}
        <DropdownMenuItem onClick={copyToClipboard}>
          {copied ? (
            <Check className="w-4 h-4 mr-2 text-green-500" />
          ) : (
            <Copy className="w-4 h-4 mr-2" />
          )}
          {copied ? "Copied!" : "Copy Link"}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact version for restaurant cards
export function SocialShareButton({
  url,
  title,
  description,
  hashtags = [],
  className = "",
}) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success("Link copied!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy link");
    }
  };

  const handleNativeShare = async () => {
    if (typeof navigator !== "undefined" && navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description,
          url: url,
        });
      } catch (err) {
        if (err.name !== "AbortError") {
          // Fallback to copy
          copyToClipboard();
        }
      }
    } else {
      // Fallback to copy
      copyToClipboard();
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleNativeShare}
      className={`p-2 hover:bg-blue-50 ${className}`}
      title="Share restaurant"
    >
      {copied ? (
        <Check className="w-4 h-4 text-green-500" />
      ) : (
        <Share2 className="w-4 h-4 text-gray-400 hover:text-blue-500" />
      )}
    </Button>
  );
}
