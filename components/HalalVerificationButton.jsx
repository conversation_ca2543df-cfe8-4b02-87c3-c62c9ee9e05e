"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useUser, SignInButton } from "@clerk/nextjs";
import { toast } from "sonner";
import { HalalIcon } from "./HalalIcon";

export function HalalVerificationButton({
  restaurantId,
  initialIsVerified = false,
  halalCount = 0,
  className = "",
}) {
  const [isVerified, setIsVerified] = useState(initialIsVerified);
  const [currentHalalCount, setCurrentHalalCount] = useState(halalCount);
  const [isLoading, setIsLoading] = useState(false);
  const { isSignedIn } = useUser();

  const handleVerification = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in to verify halal status");
      return;
    }

    setIsLoading(true);

    try {
      if (isVerified) {
        // Remove verification
        const response = await fetch(
          `/api/halal-verification/restaurant/${restaurantId}`,
          {
            method: "DELETE",
          }
        );

        if (response.ok) {
          setCurrentHalalCount((prev) => Math.max(0, prev - 1));
          setIsVerified(false);
          toast.success("Halal verification removed");
        } else {
          throw new Error("Failed to remove verification");
        }
      } else {
        // Add verification
        const response = await fetch("/api/halal-verification", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ restaurantId }),
        });

        if (response.ok) {
          setCurrentHalalCount((prev) => prev + 1);
          setIsVerified(true);
          toast.success("Marked as Halal");
        } else {
          const error = await response.json();
          throw new Error(error.message || "Failed to add verification");
        }
      }
    } catch (error) {
      console.error("Error updating halal verification:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not signed in, wrap in SignInButton
  if (!isSignedIn) {
    return (
      <SignInButton mode="modal">
        <Button
          variant="ghost"
          size="sm"
          className={`p-2 hover:bg-green-50 ${className} flex items-center gap-1`}
          title="Sign in to verify this restaurant is halal"
        >
          <HalalIcon
            className={`w-5 h-5 transition-opacity ${
              isVerified ? "opacity-100" : "opacity-60 hover:opacity-80"
            }`}
          />
          <span className="text-sm text-gray-600">{currentHalalCount}</span>
        </Button>
      </SignInButton>
    );
  }

  // For signed in users, show the verification button
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleVerification}
      disabled={isLoading}
      className={`p-2 hover:bg-green-50 ${className} flex items-center gap-1`}
      title={
        isVerified
          ? "Remove halal verification"
          : "Verify this restaurant is halal"
      }
    >
      <HalalIcon
        className={`w-5 h-5 transition-opacity ${
          isVerified ? "opacity-100" : "opacity-60 hover:opacity-80"
        } ${isLoading ? "opacity-50" : ""}`}
      />
      <span className="text-sm text-gray-600">{currentHalalCount}</span>
    </Button>
  );
}
