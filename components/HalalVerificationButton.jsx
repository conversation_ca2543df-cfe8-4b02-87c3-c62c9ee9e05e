"use client";

import { useState } from "react";
import { CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser, SignInButton } from "@clerk/nextjs";
import { toast } from "sonner";

export function HalalVerificationButton({
  restaurantId,
  initialIsVerified = false,
  halalCount = 0,
  showCount = false,
  className = "",
}) {
  const [isVerified, setIsVerified] = useState(initialIsVerified);
  const [currentHalalCount, setCurrentHalalCount] = useState(halalCount);
  const [isLoading, setIsLoading] = useState(false);
  const { isSignedIn } = useUser();

  const handleVerification = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in to verify halal status");
      return;
    }

    setIsLoading(true);

    try {
      if (isVerified) {
        // Remove verification
        const response = await fetch(
          `/api/halal-verification/restaurant/${restaurantId}`,
          {
            method: "DELETE",
          }
        );

        if (response.ok) {
          setCurrentHalalCount((prev) => Math.max(0, prev - 1));
          setIsVerified(false);
          toast.success("Halal verification removed");
        } else {
          throw new Error("Failed to remove verification");
        }
      } else {
        // Add verification
        const response = await fetch("/api/halal-verification", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ restaurantId }),
        });

        if (response.ok) {
          setCurrentHalalCount((prev) => prev + 1);
          setIsVerified(true);
          toast.success("Marked as Halal");
        } else {
          const error = await response.json();
          throw new Error(error.message || "Failed to add verification");
        }
      }
    } catch (error) {
      console.error("Error updating halal verification:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not signed in, wrap in SignInButton
  if (!isSignedIn) {
    return (
      <SignInButton mode="modal">
        <Button
          variant="ghost"
          size="sm"
          className={`p-1 hover:bg-green-50 ${className} ${
            showCount ? "flex items-center gap-1" : ""
          }`}
          title="Sign in to verify halal status"
        >
          <CheckCircle className="w-4 h-4 text-gray-400 hover:text-green-500" />
          {showCount && (
            <span className="text-xs text-gray-600 ml-1">
              {currentHalalCount}
            </span>
          )}
        </Button>
      </SignInButton>
    );
  }

  // For signed in users, show the verification button
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleVerification}
      disabled={isLoading}
      className={`p-1 hover:bg-green-50 ${className} ${
        showCount ? "flex items-center gap-1" : ""
      }`}
      title={isVerified ? "Remove halal verification" : "Mark as Halal"}
    >
      <CheckCircle
        className={`w-4 h-4 transition-colors ${
          isVerified
            ? "fill-green-500 text-green-500"
            : "text-gray-400 hover:text-green-500"
        } ${isLoading ? "opacity-50" : ""}`}
      />
      {showCount && (
        <span className="text-xs text-gray-600 ml-1">{currentHalalCount}</span>
      )}
    </Button>
  );
}
