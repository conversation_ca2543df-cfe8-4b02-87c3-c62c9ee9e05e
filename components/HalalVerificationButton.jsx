"use client";

import { useState } from "react";
import { CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser, SignInButton } from "@clerk/nextjs";
import { toast } from "sonner";

export function HalalVerificationButton({
  restaurantId,
  initialIsVerified = false,
  halalCount = 0,
  className = "",
}) {
  const [isVerified, setIsVerified] = useState(initialIsVerified);
  const [currentHalalCount, setCurrentHalalCount] = useState(halalCount);
  const [isLoading, setIsLoading] = useState(false);
  const { isSignedIn } = useUser();

  const handleVerification = async () => {
    if (!isSignedIn) {
      toast.error("Please sign in to verify halal status");
      return;
    }

    setIsLoading(true);

    try {
      if (isVerified) {
        // Remove verification
        const response = await fetch(
          `/api/halal-verification/restaurant/${restaurantId}`,
          {
            method: "DELETE",
          }
        );

        if (response.ok) {
          setCurrentHalalCount((prev) => Math.max(0, prev - 1));
          setIsVerified(false);
          toast.success("Halal verification removed");
        } else {
          throw new Error("Failed to remove verification");
        }
      } else {
        // Add verification
        const response = await fetch("/api/halal-verification", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ restaurantId }),
        });

        if (response.ok) {
          setCurrentHalalCount((prev) => prev + 1);
          setIsVerified(true);
          toast.success("Marked as Halal");
        } else {
          const error = await response.json();
          throw new Error(error.message || "Failed to add verification");
        }
      }
    } catch (error) {
      console.error("Error updating halal verification:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not signed in, wrap in SignInButton
  if (!isSignedIn) {
    return (
      <SignInButton mode="modal">
        <Button
          variant="outline"
          size="sm"
          className={`hover:bg-green-50 border-green-200 ${className}`}
          title="Sign in to verify this restaurant is halal"
        >
          <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
          <span className="text-xs text-green-700">
            {currentHalalCount > 0
              ? `${currentHalalCount} say Halal`
              : "Verify Halal"}
          </span>
        </Button>
      </SignInButton>
    );
  }

  // For signed in users, show the verification button
  return (
    <Button
      variant={isVerified ? "default" : "outline"}
      size="sm"
      onClick={handleVerification}
      disabled={isLoading}
      className={`${
        isVerified
          ? "bg-green-500 hover:bg-green-600 text-white border-green-500"
          : "hover:bg-green-50 border-green-200 text-green-700"
      } ${className}`}
      title={
        isVerified
          ? "Remove halal verification"
          : "Verify this restaurant is halal"
      }
    >
      <CheckCircle
        className={`w-4 h-4 mr-1 transition-colors ${
          isVerified ? "text-white" : "text-green-500"
        } ${isLoading ? "opacity-50" : ""}`}
      />
      <span className="text-xs">
        {isVerified
          ? "Verified Halal"
          : currentHalalCount > 0
          ? `${currentHalalCount} say Halal`
          : "Verify Halal"}
      </span>
    </Button>
  );
}
