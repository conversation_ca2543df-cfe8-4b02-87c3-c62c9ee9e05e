"use client";

import { useState } from "react";
import { CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser, SignInButton } from "@clerk/nextjs";
import { toast } from "sonner";

export function HalalVerificationButton({
  restaurantId,
  initialVerification = null, // { hasVerified: false, isHalal: null }
  halalCount = 0,
  notHalalCount = 0,
  showCount = false,
  className = "",
}) {
  const [verification, setVerification] = useState(initialVerification);
  const [currentHalalCount, setCurrentHalalCount] = useState(halalCount);
  const [currentNotHalalCount, setCurrentNotHalalCount] = useState(notHalalCount);
  const [isLoading, setIsLoading] = useState(false);
  const { isSignedIn } = useUser();

  const handleVerification = async (isHalal) => {
    if (!isSignedIn) {
      toast.error("Please sign in to verify halal status");
      return;
    }

    setIsLoading(true);

    try {
      // If user is clicking the same verification they already have, remove it
      if (verification?.hasVerified && verification?.isHalal === isHalal) {
        // Remove verification
        const response = await fetch(`/api/halal-verification/restaurant/${restaurantId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          // Update counts
          if (isHalal) {
            setCurrentHalalCount(prev => Math.max(0, prev - 1));
          } else {
            setCurrentNotHalalCount(prev => Math.max(0, prev - 1));
          }
          setVerification({ hasVerified: false, isHalal: null });
          toast.success("Verification removed");
        } else {
          throw new Error('Failed to remove verification');
        }
      } else {
        // Add or update verification
        const response = await fetch('/api/halal-verification', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ restaurantId, isHalal }),
        });

        if (response.ok) {
          // Update counts based on previous and new verification
          if (verification?.hasVerified) {
            // User is changing their verification
            if (verification.isHalal) {
              setCurrentHalalCount(prev => Math.max(0, prev - 1));
            } else {
              setCurrentNotHalalCount(prev => Math.max(0, prev - 1));
            }
          }
          
          // Add new verification count
          if (isHalal) {
            setCurrentHalalCount(prev => prev + 1);
          } else {
            setCurrentNotHalalCount(prev => prev + 1);
          }

          setVerification({ hasVerified: true, isHalal });
          toast.success(isHalal ? "Marked as Halal" : "Marked as Not Halal");
        } else {
          const error = await response.json();
          throw new Error(error.message || 'Failed to update verification');
        }
      }
    } catch (error) {
      console.error('Error updating halal verification:', error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not signed in, wrap in SignInButton
  if (!isSignedIn) {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <SignInButton mode="modal">
          <Button
            variant="ghost"
            size="sm"
            className="p-1 hover:bg-green-50"
            title="Sign in to verify halal status"
          >
            <CheckCircle className="w-4 h-4 text-gray-400 hover:text-green-500" />
          </Button>
        </SignInButton>
        {showCount && (
          <span className="text-xs text-gray-600">{currentHalalCount}</span>
        )}
        
        <SignInButton mode="modal">
          <Button
            variant="ghost"
            size="sm"
            className="p-1 hover:bg-red-50"
            title="Sign in to verify halal status"
          >
            <XCircle className="w-4 h-4 text-gray-400 hover:text-red-500" />
          </Button>
        </SignInButton>
        {showCount && (
          <span className="text-xs text-gray-600">{currentNotHalalCount}</span>
        )}
      </div>
    );
  }

  // For signed in users, show the verification buttons
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* Halal Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVerification(true)}
        disabled={isLoading}
        className="p-1 hover:bg-green-50"
        title={
          verification?.hasVerified && verification?.isHalal === true
            ? "Remove halal verification"
            : "Mark as Halal"
        }
      >
        <CheckCircle
          className={`w-4 h-4 transition-colors ${
            verification?.hasVerified && verification?.isHalal === true
              ? "fill-green-500 text-green-500"
              : "text-gray-400 hover:text-green-500"
          } ${isLoading ? "opacity-50" : ""}`}
        />
      </Button>
      {showCount && (
        <span className="text-xs text-gray-600">{currentHalalCount}</span>
      )}

      {/* Not Halal Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVerification(false)}
        disabled={isLoading}
        className="p-1 hover:bg-red-50"
        title={
          verification?.hasVerified && verification?.isHalal === false
            ? "Remove not-halal verification"
            : "Mark as Not Halal"
        }
      >
        <XCircle
          className={`w-4 h-4 transition-colors ${
            verification?.hasVerified && verification?.isHalal === false
              ? "fill-red-500 text-red-500"
              : "text-gray-400 hover:text-red-500"
          } ${isLoading ? "opacity-50" : ""}`}
        />
      </Button>
      {showCount && (
        <span className="text-xs text-gray-600">{currentNotHalalCount}</span>
      )}
    </div>
  );
}
