"use client";

import { useState } from "react";
import { Heart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser, SignInButton } from "@clerk/nextjs";
import { toast } from "sonner";

export function FavoriteButton({
  restaurantId,
  initialIsFavorited = false,
  favoritesCount = 0,
  showCount = false,
  className = "",
}) {
  const [isFavorited, setIsFavorited] = useState(initialIsFavorited);
  const [currentCount, setCurrentCount] = useState(favoritesCount);
  const [isLoading, setIsLoading] = useState(false);
  const { isSignedIn } = useUser();

  const handleToggleFavorite = async (e) => {
    e.preventDefault(); // Prevent navigation if button is inside a link
    e.stopPropagation();

    if (!isSignedIn) {
      toast.error("Please sign in to save favorites");
      return;
    }

    setIsLoading(true);

    try {
      if (isFavorited) {
        // Remove from favorites
        const response = await fetch(
          `/api/favorites/restaurant/${restaurantId}`,
          {
            method: "DELETE",
          }
        );

        if (response.ok) {
          setIsFavorited(false);
          setCurrentCount((prev) => Math.max(0, prev - 1));
          toast.success("Removed from favorites");
        } else {
          throw new Error("Failed to remove favorite");
        }
      } else {
        // Add to favorites
        const response = await fetch("/api/favorites", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ restaurantId }),
        });

        if (response.ok) {
          setIsFavorited(true);
          setCurrentCount((prev) => prev + 1);
          toast.success("Added to favorites");
        } else {
          const error = await response.json();
          throw new Error(error.message || "Failed to add favorite");
        }
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not signed in, wrap in SignInButton
  if (!isSignedIn) {
    return (
      <SignInButton mode="modal">
        <Button
          variant="ghost"
          size="sm"
          className={`p-2 hover:bg-red-50 ${className} ${
            showCount ? "flex items-center gap-1" : ""
          }`}
          title="Sign in to save favorites"
        >
          <Heart className="w-5 h-5 transition-colors text-gray-400 hover:text-red-500" />
          {showCount && (
            <span className="text-sm text-gray-600 ml-1">{currentCount}</span>
          )}
        </Button>
      </SignInButton>
    );
  }

  // For signed in users, show the regular toggle button
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleToggleFavorite}
      disabled={isLoading}
      className={`p-2 hover:bg-red-50 ${className} ${
        showCount ? "flex items-center gap-1" : ""
      }`}
      title={isFavorited ? "Remove from favorites" : "Add to favorites"}
    >
      <Heart
        className={`w-5 h-5 transition-colors ${
          isFavorited
            ? "fill-red-500 text-red-500"
            : "text-gray-400 hover:text-red-500"
        } ${isLoading ? "opacity-50" : ""}`}
      />
      {showCount && (
        <span className="text-sm text-gray-600 ml-1">{currentCount}</span>
      )}
    </Button>
  );
}
