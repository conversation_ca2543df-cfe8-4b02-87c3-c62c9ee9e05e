"use client";

import { useState } from "react";
import { Heart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useUser, SignInButton } from "@clerk/nextjs";
import { toast } from "sonner";

export function FavoriteButton({
  restaurantId,
  initialIsFavorited = false,
  favoritesCount = 0,
  className = "",
}) {
  const [isFavorited, setIsFavorited] = useState(initialIsFavorited);
  const [currentCount, setCurrentCount] = useState(favoritesCount);
  const [isLoading, setIsLoading] = useState(false);
  const { isSignedIn } = useUser();

  const handleToggleFavorite = async (e) => {
    e.preventDefault(); // Prevent navigation if button is inside a link
    e.stopPropagation();

    if (!isSignedIn) {
      toast.error("Please sign in to save favorites");
      return;
    }

    setIsLoading(true);

    try {
      if (isFavorited) {
        // Remove from favorites
        const response = await fetch(
          `/api/favorites/restaurant/${restaurantId}`,
          {
            method: "DELETE",
          }
        );

        if (response.ok) {
          setIsFavorited(false);
          setCurrentCount((prev) => Math.max(0, prev - 1));
          toast.success("Removed from favorites");
        } else {
          throw new Error("Failed to remove favorite");
        }
      } else {
        // Add to favorites
        const response = await fetch("/api/favorites", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ restaurantId }),
        });

        if (response.ok) {
          setIsFavorited(true);
          setCurrentCount((prev) => prev + 1);
          toast.success("Added to favorites");
        } else {
          const error = await response.json();
          throw new Error(error.message || "Failed to add favorite");
        }
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  // If user is not signed in, wrap in SignInButton
  if (!isSignedIn) {
    return (
      <SignInButton mode="modal">
        <Button
          variant="outline"
          size="sm"
          className={`hover:bg-red-50 border-red-200 w-full justify-center ${className}`}
          title="Sign in to save favorites"
        >
          <Heart className="w-4 h-4 text-red-500 mr-1" />
          <span className="text-xs text-red-700">
            {currentCount > 0
              ? `${currentCount} favorites`
              : "Add to Favorites"}
          </span>
        </Button>
      </SignInButton>
    );
  }

  // For signed in users, show the regular toggle button
  return (
    <Button
      variant={isFavorited ? "default" : "outline"}
      size="sm"
      onClick={handleToggleFavorite}
      disabled={isLoading}
      className={`w-full justify-center ${
        isFavorited
          ? "bg-red-500 hover:bg-red-600 text-white border-red-500"
          : "hover:bg-red-50 border-red-200 text-red-700"
      } ${className}`}
      title={isFavorited ? "Remove from favorites" : "Add to favorites"}
    >
      <Heart
        className={`w-4 h-4 mr-1 transition-colors ${
          isFavorited ? "text-white fill-white" : "text-red-500"
        } ${isLoading ? "opacity-50" : ""}`}
      />
      <span className="text-xs">
        {isFavorited
          ? "Favorited"
          : currentCount > 0
          ? `${currentCount} favorites`
          : "Add to Favorites"}
      </span>
    </Button>
  );
}
