"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";

import {
  Search,
  User,
  Menu,
  Plus,
  Heart,
  Settings,
  LogOut,
} from "lucide-react";
import { useState } from "react";
import { useUser, SignInButton, SignUpButton, UserButton } from "@clerk/nextjs";

export function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isSignedIn, user } = useUser();

  return (
    <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="text-2xl font-bold text-primary">
            🍽️ MaybeHalal
          </Link>

          {/* Desktop Navigation Menu */}
          <NavigationMenu className="hidden md:flex">
            <NavigationMenuList>
              <NavigationMenuItem>
                <Link href="/">
                  <NavigationMenuLink className="px-4 py-2 hover:text-primary transition-colors">
                    Home
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href="/restaurants">
                  <NavigationMenuLink className="px-4 py-2 hover:text-primary transition-colors">
                    Restaurants
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href="/search">
                  <NavigationMenuLink className="px-4 py-2 hover:text-primary transition-colors flex items-center gap-2">
                    <Search className="w-4 h-4" />
                    Search
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center gap-3">
            {/* Add Restaurant Button - Only show if signed in */}
            {isSignedIn && (
              <Button asChild variant="outline" size="sm">
                <Link
                  href="/restaurants/add"
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Restaurant
                </Link>
              </Button>
            )}

            {/* Favorites Button - Only show if signed in */}
            {isSignedIn && (
              <Button asChild variant="ghost" size="sm">
                <Link href="/favorites" className="flex items-center gap-2">
                  <Heart className="w-4 h-4" />
                  Favorites
                </Link>
              </Button>
            )}

            {/* Authentication */}
            {isSignedIn ? (
              <div className="flex items-center gap-2">
                {/* Admin Link */}
                {user?.publicMetadata?.role === "ADMIN" && (
                  <Button asChild variant="ghost" size="sm">
                    <Link href="/admin" className="flex items-center gap-2">
                      <Settings className="w-4 h-4" />
                      Admin
                    </Link>
                  </Button>
                )}

                {/* User Button from Clerk */}
                <UserButton
                  afterSignOutUrl="/"
                  userProfileMode="navigation"
                  userProfileUrl="/profile"
                />
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <SignInButton mode="modal">
                  <Button variant="ghost" size="sm">
                    Sign In
                  </Button>
                </SignInButton>
                <SignUpButton mode="modal">
                  <Button size="sm">Sign Up</Button>
                </SignUpButton>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Menu className="w-5 h-5" />
          </Button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t pt-4">
            <div className="flex flex-col space-y-3">
              <Link
                href="/"
                className="px-4 py-2 hover:bg-gray-100 rounded-md transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/restaurants"
                className="px-4 py-2 hover:bg-gray-100 rounded-md transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Restaurants
              </Link>
              <Link
                href="/search"
                className="px-4 py-2 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-2"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Search className="w-4 h-4" />
                Search
              </Link>
              <div className="border-t pt-3 mt-3">
                {isSignedIn ? (
                  <>
                    <Link
                      href="/restaurants/add"
                      className="px-4 py-2 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Plus className="w-4 h-4" />
                      Add Restaurant
                    </Link>
                    <Link
                      href="/favorites"
                      className="px-4 py-2 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Heart className="w-4 h-4" />
                      Favorites
                    </Link>
                    {user?.publicMetadata?.role === "ADMIN" && (
                      <Link
                        href="/admin"
                        className="px-4 py-2 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <Settings className="w-4 h-4" />
                        Admin
                      </Link>
                    )}
                  </>
                ) : (
                  <>
                    <div className="px-4 py-2">
                      <SignInButton mode="modal">
                        <Button
                          variant="ghost"
                          className="w-full justify-start"
                        >
                          Sign In
                        </Button>
                      </SignInButton>
                    </div>
                    <div className="px-4 py-2">
                      <SignUpButton mode="modal">
                        <Button className="w-full">Sign Up</Button>
                      </SignUpButton>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
