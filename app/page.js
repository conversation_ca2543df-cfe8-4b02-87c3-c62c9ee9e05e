import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SearchBar } from "@/components/search/SearchBar";
import {
  Star,
  MapPin,
  TrendingUp,
  ChefHat,
  Clock,
  Award,
  Plus,
  Heart,
} from "lucide-react";
import prisma from "@/lib/prisma";

async function getFeaturedRestaurants() {
  try {
    const restaurants = await prisma.restaurant.findMany({
      where: {
        isPublished: true,
        isFeatured: true,
      },
      include: {
        _count: {
          select: {
            favorites: true,
            halalVerifications: true,
          },
        },
      },
      take: 6,
      orderBy: {
        createdAt: "desc",
      },
    });
    return restaurants;
  } catch (error) {
    console.error("Error fetching featured restaurants:", error);
    return [];
  }
}

async function getRestaurantStats() {
  try {
    const totalRestaurants = await prisma.restaurant.count({
      where: { isPublished: true },
    });

    const totalCuisines = await prisma.restaurant.findMany({
      where: { isPublished: true },
      select: { cuisines: true },
    });

    const uniqueCuisines = new Set();
    totalCuisines.forEach((restaurant) => {
      restaurant.cuisines.forEach((cuisine) => uniqueCuisines.add(cuisine));
    });

    const totalCities = await prisma.restaurant.findMany({
      where: { isPublished: true },
      select: { city: true },
      distinct: ["city"],
    });

    return {
      restaurants: totalRestaurants,
      cuisines: uniqueCuisines.size,
      cities: totalCities.length,
    };
  } catch (error) {
    console.error("Error fetching stats:", error);
    return { restaurants: 0, cuisines: 0, cities: 0 };
  }
}

export default async function Home() {
  const [featuredRestaurants, stats] = await Promise.all([
    getFeaturedRestaurants(),
    getRestaurantStats(),
  ]);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-50 via-white to-red-50 py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            {/* Hero Content */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
                Discover Halal
                <span className="text-primary block">Restaurants Near You</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 max-w-2xl mx-auto">
                Find the perfect dining experience with our comprehensive
                restaurant directory. Search by cuisine, location, or features.
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-3xl mx-auto">
              <SearchBar size="large" />
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 md:gap-8 max-w-2xl mx-auto pt-8">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-primary">
                  {stats.restaurants}+
                </div>
                <div className="text-sm md:text-base text-gray-600">
                  Restaurants
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-primary">
                  {stats.cuisines}+
                </div>
                <div className="text-sm md:text-base text-gray-600">
                  Cuisines
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-primary">
                  {stats.cities}+
                </div>
                <div className="text-sm md:text-base text-gray-600">Cities</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Restaurants Section */}
      {featuredRestaurants.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Restaurants
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover our handpicked selection of exceptional dining
                experiences
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
              {featuredRestaurants.map((restaurant) => (
                <Card
                  key={restaurant.id}
                  className="group hover:shadow-lg transition-all duration-300"
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <CardTitle className="group-hover:text-primary transition-colors">
                          <Link href={`/restaurants/${restaurant.slug}`}>
                            {restaurant.name}
                          </Link>
                        </CardTitle>
                        <div className="flex flex-wrap gap-2">
                          <Badge
                            variant="secondary"
                            className="flex items-center gap-1"
                          >
                            <ChefHat className="w-3 h-3" />
                            {restaurant.type}
                          </Badge>
                          {restaurant.isFeatured && (
                            <Badge
                              variant="default"
                              className="flex items-center gap-1"
                            >
                              <Star className="w-3 h-3" />
                              Featured
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="w-4 h-4 mr-2" />
                        {restaurant.city}, {restaurant.state}
                      </div>

                      {restaurant.cuisines.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {restaurant.cuisines.slice(0, 3).map((cuisine) => (
                            <Badge
                              key={cuisine}
                              variant="outline"
                              className="text-xs"
                            >
                              {cuisine}
                            </Badge>
                          ))}
                          {restaurant.cuisines.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{restaurant.cuisines.length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Favorites Count */}
                      {restaurant._count?.favorites !== undefined && (
                        <div className="flex items-center text-sm text-gray-500">
                          <Heart className="w-4 h-4 mr-1 text-red-400" />
                          <span>
                            {restaurant._count.favorites}{" "}
                            {restaurant._count.favorites === 1
                              ? "favorite"
                              : "favorites"}
                          </span>
                        </div>
                      )}

                      {/* Halal Verification Count */}
                      {restaurant._count?.halalVerifications > 0 && (
                        <div className="flex items-center text-sm">
                          <div className="flex items-center gap-1 text-green-600">
                            <span className="text-xs">
                              ✓ {restaurant._count.halalVerifications} say Halal
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-12">
              <Button asChild size="lg">
                <Link href="/restaurants">View All Restaurants</Link>
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Call to Action Sections */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid gap-8 md:grid-cols-2 max-w-4xl mx-auto">
            {/* Search CTA */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <div className="space-y-4">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <TrendingUp className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">
                  Find Your Perfect Meal
                </h3>
                <p className="text-gray-600">
                  Search through hundreds of restaurants by cuisine, location,
                  or special features. Find exactly what you're craving.
                </p>
                <Button asChild size="lg" className="w-full">
                  <Link href="/search">Start Searching</Link>
                </Button>
              </div>
            </Card>

            {/* Add Restaurant CTA */}
            <Card className="p-8 text-center hover:shadow-lg transition-shadow">
              <div className="space-y-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <Plus className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">
                  List Your Restaurant
                </h3>
                <p className="text-gray-600">
                  Are you a restaurant owner? Join our directory and reach more
                  customers. It's free and easy to get started.
                </p>
                <Button asChild size="lg" variant="outline" className="w-full">
                  <Link href="/restaurants/add">Add Restaurant</Link>
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose FoodFinder?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We make it easy to discover and connect with great restaurants
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3 max-w-5xl mx-auto">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <MapPin className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                Location-Based Search
              </h3>
              <p className="text-gray-600">
                Find restaurants near you or in any city. Our location-based
                search makes discovery effortless.
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Award className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                Curated Selection
              </h3>
              <p className="text-gray-600">
                Every restaurant is carefully reviewed and categorized with
                detailed information about cuisines and features.
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                Always Updated
              </h3>
              <p className="text-gray-600">
                Our directory is constantly updated with new restaurants and the
                latest information.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
