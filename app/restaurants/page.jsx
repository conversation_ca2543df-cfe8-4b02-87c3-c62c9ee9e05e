import RestaurantCard from "@/components/restaurant/restaurant-card";
import {
  PaginationControls,
  PaginationInfo,
} from "@/components/PaginationControls";
import prisma from "@/lib/prisma";

const ITEMS_PER_PAGE = 12;

const RestaurantsPage = async ({ searchParams }) => {
  const params = await searchParams;
  const page = parseInt(params.page) || 1;
  const offset = (page - 1) * ITEMS_PER_PAGE;

  const [restaurants, totalCount] = await Promise.all([
    prisma.restaurant.findMany({
      where: { isPublished: true },
      include: {
        _count: {
          select: {
            favorites: true,
          },
        },
      },
      orderBy: [{ isFeatured: "desc" }, { createdAt: "desc" }],
      skip: offset,
      take: ITEMS_PER_PAGE,
    }),
    prisma.restaurant.count({
      where: { isPublished: true },
    }),
  ]);

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
            All Restaurants
          </h1>
          <p className="text-lg text-gray-600">
            Discover amazing dining experiences in your area
          </p>
        </div>

        {/* Pagination Info */}
        <div className="flex justify-between items-center">
          <PaginationInfo
            currentPage={page}
            totalPages={totalPages}
            totalItems={totalCount}
            itemsPerPage={ITEMS_PER_PAGE}
          />
        </div>

        {/* Restaurants Grid */}
        {restaurants.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {restaurants.map((restaurant) => (
              <RestaurantCard key={restaurant.id} restaurant={restaurant} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold mb-2">No restaurants found</h3>
            <p className="text-gray-600">
              Be the first to add a restaurant to our directory!
            </p>
          </div>
        )}

        {/* Pagination Controls */}
        <PaginationControls
          currentPage={page}
          totalPages={totalPages}
          baseUrl="/restaurants"
          className="mt-8"
        />
      </div>
    </div>
  );
};

export default RestaurantsPage;
