import { notFound } from "next/navigation";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";
import {
  MapPin,
  Phone,
  Globe,
  Clock,
  Star,
  Calendar,
  ChefHat,
  Sparkles,
  Edit,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { FavoriteButton } from "@/components/FavoriteButton";
import { HalalVerificationButton } from "@/components/HalalVerificationButton";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";

async function getRestaurant(slug) {
  const restaurant = await prisma.restaurant.findUnique({
    where: { slug },
    include: {
      owner: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      _count: {
        select: {
          favorites: true,
          halalVerifications: true,
        },
      },
    },
  });

  if (!restaurant) return null;
  return restaurant;
}

const RestaurantPage = async ({ params }) => {
  // params are coming from [slug] / url
  const { slug } = await params;
  const [restaurant, user] = await Promise.all([
    getRestaurant(slug),
    getCurrentUser(),
  ]);

  if (!restaurant) {
    notFound();
  }

  // Check if user can edit this restaurant
  const canEdit =
    user &&
    (user.role === "ADMIN" ||
      (user.role === "OWNER" && restaurant.ownerId === user.id) ||
      restaurant.ownerId === user.id);

  // Check if user has favorited this restaurant and halal verification status
  let isFavorited = false;
  let isHalalVerified = false;
  if (user) {
    try {
      const [favorite, halalVerificationRecord] = await Promise.all([
        prisma.favorite.findUnique({
          where: {
            userId_restaurantId: {
              userId: user.id,
              restaurantId: restaurant.id,
            },
          },
        }),
        prisma.halalVerification.findUnique({
          where: {
            userId_restaurantId: {
              userId: user.id,
              restaurantId: restaurant.id,
            },
          },
        }),
      ]);
      isFavorited = !!favorite;
      isHalalVerified = !!halalVerificationRecord;
    } catch (error) {
      console.error("Error checking user status:", error);
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/restaurants">Restaurants</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{restaurant.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-4xl mx-auto space-y-6">
        {/* Hero Section */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CardTitle className="text-3xl md:text-4xl">
                    {restaurant.name}
                  </CardTitle>
                  {restaurant.isFeatured && (
                    <Badge
                      variant="default"
                      className="flex items-center gap-1"
                    >
                      <Star className="w-3 h-3" />
                      Featured
                    </Badge>
                  )}
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    <ChefHat className="w-3 h-3" />
                    {restaurant.type || "Restaurant"}
                  </Badge>
                  {!restaurant.isPublished && (
                    <Badge
                      variant="outline"
                      className="text-orange-600 border-orange-600"
                    >
                      Draft
                    </Badge>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                {/* Favorite Button */}
                <FavoriteButton
                  restaurantId={restaurant.id}
                  initialIsFavorited={isFavorited}
                  favoritesCount={restaurant._count?.favorites || 0}
                  showCount={true}
                />

                {/* Halal Verification Button */}
                <HalalVerificationButton
                  restaurantId={restaurant.id}
                  initialIsVerified={isHalalVerified}
                  halalCount={restaurant._count?.halalVerifications || 0}
                  showCount={true}
                />

                {/* Edit Button */}
                {canEdit && (
                  <Button asChild variant="outline">
                    <Link href={`/restaurants/${restaurant.slug}/edit`}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Restaurant
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Contact & Location Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {/* <MapPin className="w-5 h-5" /> */}
              Location & Contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Address */}
            <div>
              <div className="flex items-start gap-3">
                <MapPin className="w-5 h-5 mt-0.5 text-muted-foreground" />
                <div>
                  <p className="font-medium">{restaurant.address}</p>
                  <p className="text-muted-foreground">
                    {restaurant.city}, {restaurant.state} {restaurant.zipcode}
                  </p>
                </div>
              </div>
              <Button asChild variant="outline" className="mt-2 ml-3">
                <Link
                  href={`https://www.google.com/maps/search/?api=1&query=${restaurant.address}+${restaurant.city}+${restaurant.state}+${restaurant.zipcode}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Get Directions
                </Link>
              </Button>
            </div>

            {/* Contact Info */}
            <div className="flex flex-col sm:flex-row gap-4">
              {restaurant.phone && (
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  asChild
                >
                  <a href={`tel:${restaurant.phone}`}>
                    <Phone className="w-4 h-4" />
                    {restaurant.phone}
                  </a>
                </Button>
              )}
              {restaurant.website && (
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  asChild
                >
                  <a
                    href={restaurant.website}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Globe className="w-4 h-4" />
                    Visit Website
                  </a>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Cuisines & Highlights Section */}
        {(restaurant.cuisines?.length > 0 ||
          restaurant.highlights?.length > 0) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Cuisines & Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Cuisines */}
              {restaurant.cuisines?.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <ChefHat className="w-4 h-4" />
                    Cuisines
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {restaurant.cuisines.map((cuisine) => (
                      <Badge key={cuisine} variant="secondary">
                        {cuisine}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Separator if both sections exist */}
              {restaurant.cuisines?.length > 0 &&
                restaurant.highlights?.length > 0 && <Separator />}

              {/* Highlights */}
              {restaurant.highlights?.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    Features & Highlights
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {restaurant.highlights.map((highlight) => (
                      <Badge key={highlight} variant="outline">
                        {highlight}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Additional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">Added:</span>
                <span>
                  {new Date(restaurant.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">Updated:</span>
                <span>
                  {new Date(restaurant.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RestaurantPage;
