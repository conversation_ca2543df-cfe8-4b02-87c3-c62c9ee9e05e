import { Suspense } from "react";
import { SearchBar } from "@/components/search/SearchBar";
import RestaurantCard from "@/components/restaurant/restaurant-card";
import {
  PaginationControls,
  PaginationInfo,
} from "@/components/PaginationControls";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

const ITEMS_PER_PAGE = 12;

async function searchRestaurants(searchParams) {
  const { q, location, page = 1 } = searchParams;
  const currentPage = parseInt(page) || 1;
  const offset = (currentPage - 1) * ITEMS_PER_PAGE;

  try {
    const whereClause = {
      isPublished: true,
      AND: [],
    };

    // Text search across name, cuisines, and highlights
    if (q) {
      whereClause.AND.push({
        OR: [
          { name: { contains: q, mode: "insensitive" } },
          { cuisines: { hasSome: [q] } },
          { highlights: { hasSome: [q] } },
          { type: { contains: q, mode: "insensitive" } },
        ],
      });
    }

    // Location search
    if (location) {
      whereClause.AND.push({
        OR: [
          { city: { contains: location, mode: "insensitive" } },
          { state: { contains: location, mode: "insensitive" } },
          { zipcode: { contains: location, mode: "insensitive" } },
        ],
      });
    }

    const [restaurants, totalCount, user] = await Promise.all([
      prisma.restaurant.findMany({
        where: whereClause,
        include: {
          _count: {
            select: {
              favorites: true,
              halalVerifications: true,
            },
          },
          halalVerifications: {
            select: {
              isHalal: true,
            },
          },
        },
        orderBy: [{ isFeatured: "desc" }, { createdAt: "desc" }],
        skip: offset,
        take: ITEMS_PER_PAGE,
      }),
      prisma.restaurant.count({
        where: whereClause,
      }),
      getCurrentUser(),
    ]);

    // Get user's favorites and halal verifications if user is logged in
    let userFavorites = new Set();
    let userHalalVerifications = new Map();
    if (user) {
      try {
        const [favorites, halalVerifications] = await Promise.all([
          prisma.favorite.findMany({
            where: { userId: user.id },
            select: { restaurantId: true },
          }),
          prisma.halalVerification.findMany({
            where: { userId: user.id },
            select: { restaurantId: true, isHalal: true },
          }),
        ]);
        userFavorites = new Set(favorites.map((f) => f.restaurantId));
        userHalalVerifications = new Map(
          halalVerifications.map((h) => [
            h.restaurantId,
            { hasVerified: true, isHalal: h.isHalal },
          ])
        );
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    }

    return {
      restaurants,
      totalCount,
      currentPage,
      userFavorites,
      userHalalVerifications,
    };
  } catch (error) {
    console.error("Error searching restaurants:", error);
    return {
      restaurants: [],
      totalCount: 0,
      currentPage: 1,
      userFavorites: new Set(),
      userHalalVerifications: new Map(),
    };
  }
}

function SearchResults({ searchParams }) {
  return (
    <Suspense fallback={<SearchResultsSkeleton />}>
      <SearchResultsContent searchParams={searchParams} />
    </Suspense>
  );
}

async function SearchResultsContent({ searchParams }) {
  const {
    restaurants,
    totalCount,
    currentPage,
    userFavorites,
    userHalalVerifications,
  } = await searchRestaurants(searchParams);
  const { q, location } = searchParams;
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  const hasSearchTerms = q || location;
  const searchSummary = [];
  if (q) searchSummary.push(`"${q}"`);
  if (location) searchSummary.push(`in ${location}`);

  return (
    <div className="space-y-6">
      {/* Search Summary */}
      {hasSearchTerms && (
        <div className="flex flex-wrap items-center gap-4">
          <h2 className="text-xl font-semibold">
            {restaurants.length} results{" "}
            {searchSummary.length > 0 && `for ${searchSummary.join(" ")}`}
          </h2>
          <div className="flex flex-wrap gap-2">
            {q && <Badge variant="secondary">Search: {q}</Badge>}
            {location && <Badge variant="outline">Location: {location}</Badge>}
          </div>
        </div>
      )}

      {/* Pagination Info */}
      {hasSearchTerms && restaurants.length > 0 && (
        <PaginationInfo
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalCount}
          itemsPerPage={ITEMS_PER_PAGE}
        />
      )}

      {/* Results */}
      {restaurants.length > 0 ? (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {restaurants.map((restaurant) => {
              // Calculate halal verification counts
              const halalCount = restaurant.halalVerifications.filter(
                (v) => v.isHalal
              ).length;
              const notHalalCount = restaurant.halalVerifications.filter(
                (v) => !v.isHalal
              ).length;

              return (
                <RestaurantCard
                  key={restaurant.id}
                  restaurant={restaurant}
                  isFavorited={userFavorites.has(restaurant.id)}
                  halalVerification={
                    userHalalVerifications.get(restaurant.id) || {
                      hasVerified: false,
                      isHalal: null,
                    }
                  }
                  halalCount={halalCount}
                  notHalalCount={notHalalCount}
                />
              );
            })}
          </div>

          {/* Pagination Controls */}
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            baseUrl="/search"
            className="mt-8"
          />
        </>
      ) : hasSearchTerms ? (
        <Card className="p-8 text-center">
          <CardContent>
            <h3 className="text-lg font-semibold mb-2">No restaurants found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search terms or location
            </p>
            <div className="space-y-2 text-sm text-gray-500">
              <p>• Check your spelling</p>
              <p>• Try broader search terms</p>
              <p>• Search by cuisine type or city name</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold mb-4">Search Restaurants</h2>
          <p className="text-gray-600">
            Use the search bar above to find restaurants by name, cuisine, or
            location
          </p>
        </div>
      )}
    </div>
  );
}

function SearchResultsSkeleton() {
  return (
    <div className="space-y-6">
      <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="p-6 space-y-4">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="flex gap-2">
                <div className="h-6 bg-gray-200 rounded w-16"></div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default async function SearchPage({ searchParams }) {
  const params = await searchParams;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
            Find Your Perfect Restaurant
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Search by restaurant name, cuisine type, or location to discover
            amazing dining experiences
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-3xl mx-auto">
          <SearchBar />
        </div>

        {/* Search Results */}
        <SearchResults searchParams={params} />
      </div>
    </div>
  );
}
